<body onload="<%=setFocus%>" bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" >

<BR>
<div align="center" class="title">
<%=titleStr%>検索
</div>

<form name="MainForm" method="post" action="app">
<input type="hidden" name="level" value="<%=syotaikeiTable.getParameter("level")%>" >

<%
if( intLevel == 1 ) {
%>
<div align="center">
  <table border="0" cellspacing="0" cellpadding="3" style="font-weight: bold;">
    <col style="width:100px;">
    <col style="width:200px;">
    <tr bgcolor="#555555"  border="1" >
      <td style="width: 100px;">
        <font color="white">&nbsp;部門名称</font>
      </td>
      <td style="width: 200px;">
        <input type="text" name="bunrui1_na" ALIGN="middle" maxlength="20" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("bunrui1_na"))%>" size="20" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
    <td colspan="4" style="font-size: 14px; font-weight:normal;" >
        <font color="white">部門名称はあいまい検索となります。</font>
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
 <td colspan="4" align="center">
        <input type="button" name="search" value="検索" onClick="syotaikeiSearch(<%=intLevel%>);" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
  </table>
</div>
<%
} else if( intLevel == 2 ) {
%>
<div align="center">
  <table border="0" cellspacing="0" cellpadding="3" style="font-weight: bold;">
    <col style="width:100px;">
    <col style="width:160px;">
    <col style="width:100px;">
    <col style="width:160px;">
    <tr bgcolor="#555555"  border="1" >
      <td style="width:100px;">

        <font color="white">&nbsp;*部門コード</font>

      </td>
      <td style="width:160px;">
      	<%
		if( MDWareHTMLUtility.toText(syotaikeiTable.getParameter("bunrui1_cd")) == "" ) {
		%>
        	<input type="text" name="bunrui1_cd" ALIGN="middle" maxlength="2" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("bunrui1_cd"))%>" size="10" tabIndex="<%= tabIndex++ %>">
      	<%
      	}else{
      	%>
      		<input type="text" name="bunrui1_cd" ALIGN="middle" maxlength="2" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("bunrui1_cd"))%>" size="10" tabIndex="-1" id="no_input_text" readonly >
      	<%
      	}
      	%>
      </td>
      <td style="width:100px;">

        <font color="white">&nbsp;大分類名称</font>

      </td>
      <td style="width:160px;">
        <input type="text" name="bunrui2_na" ALIGN="middle" maxlength="20" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("bunrui2_na"))%>" size="20" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td colspan="4" style="font-size: 14px; font-weight:normal;" >

        <font color="white">大分類名称はあいまい検索となります。</font>

      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td colspan="4" align="center">
        <input type="button" name="search" value="検索" onClick="syotaikeiSearch(<%=intLevel%>);" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
  </table>
</div>
<%
} else if( intLevel == 3 ) {
%>
<div align="center">
  <table border="0" cellspacing="0" cellpadding="3" style="font-weight: bold;">
    <col style="width:100px;">
    <col style="width:160px;">
    <col style="width:100px;">
    <col style="width:160px;">
    <tr bgcolor="#555555"  border="1" >
      <td style="width:100px;">

        <font color="white">&nbsp;*部門コード</font>

      </td>
      <td style="width:160px;">
      	<input type="text" name="bunrui1_cd" ALIGN="middle" maxlength="2" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("bunrui1_cd"))%>" size="10" tabIndex="<%= tabIndex++ %>">
      </td>
      <td style="width:100px;">

        <font color="white">&nbsp;*大分類コード</font>

      </td>
      <td style="width:160px;">
        <input type="text" name="bunrui2_cd" ALIGN="middle" maxlength="4" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("bunrui2_cd"))%>" size="10" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td>
        <font color="white">&nbsp;小分類名称</font>
      </td>
      <td width="">
        <input type="text" name="bunrui5_na" ALIGN="middle" maxlength="20" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("bunrui5_na"))%>" size="20" tabIndex="<%= tabIndex++ %>">
      </td>
      <td width="">
      </td>
      <td width="">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td colspan="4" style="font-size: 14px; font-weight:normal;" >
        <font color="white">小分類名称はあいまい検索となります。</font>
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td colspan="4" align="center">
        <input type="button" name="search" value="検索" onClick="syotaikeiSearch(<%=intLevel%>);" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
  </table>
</div>
<%
}
%>

<br>
<!------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------>
<tr height="11%">
<td>
<table width="80%" border="0" cellspacing="0" cellpadding="2" class="guide" align="center">
  <tr>
    <td>
    <CENTER>
      <table width="80%" border="0" cellspacing="0" cellpadding="2" class="guide">
        <tr><td align="center">
          <jsp:include page="ptl000002_InfoStringList.jsp" />
        </td></tr>
      </table>
    </CENTER>
    </td>
  </tr>
</table>
</td>
</tr>
</table>
<!-- 遷移画面先指定 -->
<input type="hidden" name="JobID"         value="">
<input type="hidden" name="movePage"       value="">
<!-- 次ページ指定 -->
<input type=hidden name=move value="">

<%
if( intLevel == 1 ) {
%>
<table align="center" cellspacing="0" cellpadding="0" >
  <tr>
    <td>
      <table align="left" valign="top" width="" height="20" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
        <col style="width:150px;">
        <col style="width:250px;">
        <col style="width:100px;">
        <tr bordercolor="#99FFFF" bgcolor="e8e8b0" align="center">
          <td nowrap bordercolor="#99FFFF" style="width:150px;">

            <div align="center"><font color="#000000">部門コード</font></div>


          </td>
          <td nowrap bordercolor="#99FFFF" style="width:250px;">

            <div align="center"><font color="#000000">部門名称</font></div>

          </td>
          <td nowrap bordercolor="#99FFFF" style="width:100px;">
            <div align="center"><font color="#000000">&nbsp;</font></div>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>

<%
Iterator ite = syotaikeiList.getBeanIterator();
String overF = "scroll";
if( !ite.hasNext() ) overF = "hidden";
%>

      <div align="left" style="overflow-y: <%=overF%>;WIDTH: 530px; HEIGHT: 290px;">
        <table valign="top" align="left" width="" height="" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
          <col style="width:150px;" align="left">
          <col style="width:250px;" align="left">
          <col style="width:100px; " align="center">

<%
popA60101_BmKindLineUnitBean popBean = new popA60101_BmKindLineUnitBean();

	while(ite.hasNext()){
		popBean = (popA60101_BmKindLineUnitBean)ite.next();

%>
          <tr align="center" height="35" bordercolor="#99FFFF" bgcolor="#FFFFFF">
            <td style="width:150px;"><%=MDWareHTMLUtility.toLabel( popBean.getDptCd() )%></td>
            <td style="width:250px;"><%=MDWareHTMLUtility.toLabel( popBean.getDptNa() )%></td>
            <td style="width:100px;"><input type="button" name="select" value="選択" style="width:80px; height:30px; font-size:16px;" onClick="doCommit('<%=MDWareHTMLUtility.toLabel( popBean.getDptCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDptNa() )%>','','','','');" tabIndex="<%= tabIndex++ %>"></td>
          </tr>
<%
	}
%>

</table>
</div>
</td></tr></table>

<%
} else if( intLevel == 2 ) {
%>

<table align="center" cellspacing="0" cellpadding="0" >
  <tr>
    <td>
      <table align="left" valign="top" width="" height="20" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
        <col style="width:150px;">
        <col style="width:250px;">
        <col style="width:100px;">
        <tr bordercolor="#99FFFF" bgcolor="#e8e8b0" align="center">
          <td nowrap bordercolor="#99FFFF" style="width:150px;">

            <div align="center"><font color="#000000">大分類コード</font></div>

          </td>
          <td nowrap bordercolor="#99FFFF" style="width:250px;">

            <div align="center"><font color="#000000">大分類名称</font></div>

          </td>
          <td nowrap bordercolor="#99FFFF" style="width:100px;">
            <div align="center"><font color="#000000">&nbsp;</font></div>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>

<%
Iterator ite = syotaikeiList.getBeanIterator();
String overF = "scroll";
if( !ite.hasNext() ) overF = "hidden";
%>
      <div align="left" style="overflow-y: <%=overF%>;WIDTH: 530px; HEIGHT: 290px;">
        <table valign="top" align="left" width="" height="" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
          <col style="width:150px;" align="left">
          <col style="width:250px;" align="left">
          <col style="width:100px; " align="center">

<%
popA60101_BmKindLineUnitBean popBean = new popA60101_BmKindLineUnitBean();

	while(ite.hasNext()){
		popBean = (popA60101_BmKindLineUnitBean)ite.next();

%>
          <tr align="center" height="35" bordercolor="#99FFFF" bgcolor="#FFFFFF">
            <td style="width:150px;"><%=MDWareHTMLUtility.toLabel( popBean.getLineCd() )%></td>
            <td style="width:250px;"><%=MDWareHTMLUtility.toLabel( popBean.getLineNa() )%></td>
            <td style="width:100px;"><input type="button" name="select" value="選択" style="width:80px; height:30px; font-size:16px;" onClick="doCommit('<%=MDWareHTMLUtility.toLabel( popBean.getDptCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDptNa() )%>','<%=MDWareHTMLUtility.toLabel( popBean.getLineCd() )%>','<%=MDWareHTMLUtility.toLabel( popBean.getLineNa() )%>','','');" tabIndex="<%= tabIndex++ %>"></td>
          </tr>
<%
	}
%>

</table>
</div>
</td></tr></table>

<%
} else if( intLevel == 3 ) {
%>

<table align="center" cellspacing="0" cellpadding="0" >
  <tr>
    <td>
      <table align="left" valign="top" width="" height="20" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
        <col style="width:150px;">
        <col style="width:250px;">
        <col style="width:100px;">
        <tr bordercolor="#99FFFF" bgcolor="#e8e8b0" align="center">
          <td nowrap bordercolor="#99FFFF" style="width:150px;">
            <div align="center"><font color="#000000">小分類コード</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:250px;">
            <div align="center"><font color="#000000">小分類名称</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:100px;">
            <div align="center"><font color="#000000">&nbsp;</font></div>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>

<%
Iterator ite = syotaikeiList.getBeanIterator();
String overF = "scroll";
if( !ite.hasNext() ) overF = "hidden";
%>

      <div align="left" style="overflow-y: <%=overF%>;WIDTH: 530px; HEIGHT: 290px;">
        <table valign="top" align="left" width="" height="" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
          <col style="width:150px;" align="left">
          <col style="width:250px;" align="left">
          <col style="width:100px; " align="center">

<%
popA60101_BmKindLineUnitBean popBean = new popA60101_BmKindLineUnitBean();

	while(ite.hasNext()){
		popBean = (popA60101_BmKindLineUnitBean)ite.next();

%>

          <tr align="center" height="35" bordercolor="#99FFFF" bgcolor="#FFFFFF">
            <td style="width:150px;"><%=MDWareHTMLUtility.toLabel( popBean.getClassCd() )%></td>
            <td style="width:250px;"><%=MDWareHTMLUtility.toLabel( popBean.getClassNa() )%></td>
            <td style="width:100px;"><input type="button" name="select" value="選択" style="width:80px; height:30px; font-size:16px;" onClick="doCommit('<%=MDWareHTMLUtility.toLabel( popBean.getDptCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDptNa() )%>','<%=MDWareHTMLUtility.toLabel( popBean.getLineCd() )%>','<%=MDWareHTMLUtility.toLabel( popBean.getLineNa() )%>','<%=MDWareHTMLUtility.toLabel( popBean.getClassCd() )%>','<%=MDWareHTMLUtility.toLabel( popBean.getClassNa() )%>');" tabIndex="<%= tabIndex++ %>"></td>
          </tr>
<%
	}
%>

</table>
</div>
</td></tr></table>
<%
}
%>

<table width="100%" border="0" cellpadding="5" cellspacing="0">
  <tr>
    <td width="100%" height="20" align="center" valign="top">
      <input  type="button" name="canceler" value="キャンセル" class="btn" onClick="javascript:self.window.top.close();" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
    </td>
  </tr>
</table>

</form>
</body>