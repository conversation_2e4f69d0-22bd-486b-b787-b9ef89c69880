<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" >

<BR>
<div align="center" class="title">
等級検索
</div>
<form name="MainForm" method="post" action="app">
<input type="hidden" name="torihikisaki_cd" value="<%=tokaiqTable.getParameter("torihikisaki_cd")%>">
<div align="center">
  <table border="0" cellspacing="0" cellpadding="3" style="font-weight: bold;">
    <col style="width:100px;">
    <col style="width:160px;">
    <col style="width:100px;">
    <col style="width:160px;">
    <tr bgcolor="#555555"  border="1" >
      <td style="width:100px;">
        <font color="white">&nbsp;等級コード</font>
      </td>
      <td style="width:160px;">
        <input type="text" name="tokaiq_cd" ALIGN="middle" maxlength="3" value="<%=MDWareHTMLUtility.toText(tokaiqTable.getParameter("tokaiq_cd"))%>" size="3" tabIndex="<%= tabIndex++ %>">
      </td>
      <td style="width:100px;">
        <font color="white">&nbsp;等級名称</font>
      </td>
      <td style="width:160px;">
        <input type="text" name="tokaiq_na" ALIGN="middle" maxlength="10" value="<%=MDWareHTMLUtility.toText(tokaiqTable.getParameter("tokaiq_na"))%>" size="20" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td colspan="4" style="font-size: 14px; font-weight:normal;" >
        <font color="white">等級コード、等級名称ともにあいまい検索となります。</font>
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td colspan="4" align="center">
        <input type="button" name="search" value="検索" onClick="tokaiqSearch();" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
  </table>
</div>
<br>
<!------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------>
<tr height="11%">
<td>
<table width="80%" border="0" cellspacing="0" cellpadding="2" class="guide" align="center">
  <tr>
    <td>
    <CENTER>
      <table width="80%" border="0" cellspacing="0" cellpadding="2" class="guide">
        <tr><td align="center">
          <jsp:include page="ptl000002_InfoStringList.jsp" />
        </td></tr>
      </table>
    </CENTER>
    </td>
  </tr>
</table>
</td>
</tr>
</table>
<!---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->

<!-- 遷移画面先指定 -->
<input type="hidden" name="JobID"         value="">
<input type="hidden" name="movePage"       value="">
<!-- 次ページ指定 -->
<input type=hidden name=move value="">


<table align="center" cellspacing="0" cellpadding="0" >
  <tr>
    <td>
      <table align="left" valign="top" width="" height="20" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
        <col style="width:150px;">
        <col style="width:250px;">
        <col style="width:100px;">
        <tr bordercolor="#99FFFF" bgcolor="#E8E8B0" align="center">
          <td nowrap bordercolor="#99FFFF" style="width:150px;">
            <div align="center"><font color="#000000">等級コード</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:250px;">
            <div align="center"><font color="#000000">等級名称</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:100px;">
            <div align="center"><font color="#000000">&nbsp;</font></div>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td>
<%
Iterator ite = tokaiqList.getBeanIterator();
String overF = "scroll";
if( !ite.hasNext() ) overF = "hidden";
%>
      <div align="left" style="overflow-y: <%=overF%>;WIDTH: 530px; HEIGHT: 290px;">
        <table valign="top" align="left" width="" height="" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
          <col style="width:150px;" align="left">
          <col style="width:250px;" align="left">
          <col style="width:100px; " align="center">
<%
popS02102_TokaiqSelBean popBean = new popS02102_TokaiqSelBean();
String tokaiqStr = "";
MDWareStringUtility msut = null;
while(ite.hasNext()){
	popBean = (popS02102_TokaiqSelBean)ite.next();
%>
          <tr align="center" height="35" bordercolor="#99FFFF" bgcolor="#FFFFFF">
			<td><%=MDWareHTMLUtility.toLabel( popBean.getTokaiqCd() )%></td>
            <td><%=MDWareHTMLUtility.toLabel( popBean.getTokaiqNa() )%></td>
            <td><input type="button" name="select" value="選択" style="width:80px; height:30px; font-size:16px;" onClick="doCommit('<%=MDWareHTMLUtility.toLabel( popBean.getTokaiqCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getTokaiqNa() )%>');" tabIndex="<%= tabIndex++ %>"></td>
          </tr>
<%
}
%>
</table>
</div>
</td></tr></table>

  <span id="buttonPager" style="">
    <table width="100%">
      <col width="100%" align="center" valign="top" />
      <tr>
      <td>
			<%

			String first = "";
			String prev = "";
			String next = "";
			String last = "";

			if ( tokaiqList.getCurrentPageNumber() == 1)
			{
				first = "disabled";
				prev  = "disabled";
			}
			if ( tokaiqList.getCurrentPageNumber() == tokaiqList.getLastPageNumber() || (tokaiqList.getLastPageNumber() == 0))
			{
				next = "disabled";
				last = "disabled";
			}
			%>
			<%if( first.length() > 0 ) {%>
				<input type="button" name="headPageButton" value="先　頭" onClick="" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" disabled="true">
			<%} else {%>
				<input type="button" name="headPageButton" value="先　頭" onClick="changePage('first');" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
			<%}%>
			<%if( prev.length() > 0 ) {%>
				<input type="button" name="prevPageButton" value="前　頁" onClick="" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" disabled="true">
			<%} else {%>
				<input type="button" name="prevPageButton" value="前　頁" onClick="changePage('prev');" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
			<%}%>
			<%if( next.length() > 0 ) {%>
				<input type="button" name="nextPageButton" value="次　頁" onClick="" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" disabled="true">
			<%} else {%>
				<input type="button" name="nextPageButton" value="次　頁" onClick="changePage('next');" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
			<%}%>
			<%if( last.length() > 0 ) {%>
				<input type="button" name="lastPageButton" value="最　終" onClick="" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" disabled="true"><br />
			<%} else {%>
				<input type="button" name="lastPageButton" value="最　終" onClick="changePage('last');" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>"><br />
			<%}%>
        <span id="pagingInfo" style="">( <%=tokaiqList.getMaxRows()%>件中<%=tokaiqList.getStartRowInPage()%>～<%=tokaiqList.getEndRowInPage()%>件目 )</span></span>
      </td>
      </tr>
    </table>
  </span>

<table width="100%" border="0" cellpadding="5" cellspacing="0">
  <tr>
    <td width="100%" height="20" align="center" valign="top">
      <input  type="button" name="canceler" value="キャンセル" class="btn" onClick="javascript:self.window.top.close();" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
    </td>
  </tr>
</table>
</form>
</body>