<body onload="document.MainForm.torihikisaki_cd.focus()" bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" >
<BR>
<div align="center" class="title">
代替商品検索
</div>
<br>
<form name="MainForm" method="post" action="app">
<input type="hidden" name="hachu_dt" value="<%=daitaiTable.getParameter("hachu_dt")%>">
<input type="hidden" name="nohin_dt" value="<%=daitaiTable.getParameter("nohin_dt")%>">
<input type="hidden" name="syohin_cd" value="<%=daitaiTable.getParameter("syohin_cd")%>">

<div align="center">
  <table border="0" cellspacing="0" cellpadding="3" style="font-weight: bold;">
    <col style="width:100px;">
    <col style="width:160px;">
    <col style="width:100px;">
    <col style="width:160px;">
    <tr bgcolor="#555555"  border="1" >
      <td style="width:100px;">
        <font color="white">&nbsp;*商品コード</font>
      </td>
      <td style="width:160px;">
        <input type="text" name="syohinCd" ALIGN="middle" maxlength="20" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("syohin_cd"))%>" size="20"  id="no_input_text" tabindex="-1" readonly >
      </td>
      <td style="width:100px;">
      </td>
      <td style="width:160px;">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td>
        <font color="white">&nbsp;取引先コード</font>
      </td>
      <td width="">
        <input type="text" name="torihikisaki_cd" ALIGN="middle" maxlength="6" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("torihikisaki_cd"))%>" size="8" tabIndex="<%= tabIndex++ %>">
	  </td>
      <td>
        <font color="white">&nbsp;取引先名称</font>
      </td>
      <td width="">
        <input type="text" name="torihikisaki_na" ALIGN="middle" maxlength="20" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("torihikisaki_na"))%>" size="30" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td>
        <font color="white">&nbsp;産地コード</font>
      </td>
      <td width="">
        <input type="text" name="santi_cd" ALIGN="middle" maxlength="6" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("santi_cd"))%>" size="8" tabIndex="<%= tabIndex++ %>">
      </td>
      <td>
        <font color="white">&nbsp;産地名称</font>
      </td>
      <td width="">
        <input type="text" name="santi_na" ALIGN="middle" maxlength="20" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("santi_na"))%>" size="30" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td>
        <font color="white">&nbsp;等級コード</font>
      </td>
      <td width="">
        <input type="text" name="tokaikyu_cd" ALIGN="middle" maxlength="3" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("tokaikyu_cd"))%>" size="8" tabIndex="<%= tabIndex++ %>">
      </td>
      <td>
        <font color="white">&nbsp;等級名称</font>
      </td>
      <td width="">
        <input type="text" name="tokaikyu_na" ALIGN="middle" maxlength="10" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("tokaikyu_na"))%>" size="30" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td>
        <font color="white">&nbsp;規格コード</font>
      </td>
      <td width="">
        <input type="text" name="kikaku_cd" ALIGN="middle" maxlength="3" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("kikaku_cd"))%>" size="8" tabIndex="<%= tabIndex++ %>">
      </td>
      <td>
        <font color="white">&nbsp;規格名称</font>
      </td>
      <td width="">
        <input type="text" name="kikaku_na" ALIGN="middle" maxlength="10" value="<%=MDWareHTMLUtility.toText(daitaiTable.getParameter("kikaku_na"))%>" size="30" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td colspan="4" style="font-size: 14px; font-weight:normal;" >
        <font color="white">コード、名称を入力した場合はあいまい検索となります。</font><br />
        <font color="white">対象件数が多い場合、検索及び改ページに時間がかかることがあります。</font>
      </td>
    </tr>
    <tr bgcolor="#555555"  border="1" >
      <td colspan="4" align="center">
        <input type="button" name="search" value="検索" onClick="daitaiSearch();"  style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
      </td>
    </tr>
  </table>
</div>

<br>
<table width="80%" border="0" cellspacing="0" cellpadding="2" class="guide" align="center">
	<tr>
		<td>
			<jsp:include page="ptl000002_InfoStringList.jsp" />
		</td>
	</tr>
</table>

<!-- 遷移画面先指定 -->
<input type="hidden" name="JobID"         value="">
<input type="hidden" name="movePage"       value="">
<!-- 次ページ指定 -->
<input type=hidden name=move value="">

<table align="center" cellspacing="0" cellpadding="0" >
  <tr>
    <td>
      <table align="left" valign="top" width="815px" height="20" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
        <col style="width:150px;">
        <col style="width:100px;">
        <col style="width:100px;">
        <col style="width:100px;">
        <col style="width:70px;">
        <col style="width:70px;">
        <col style="width:60px;">
        <col style="width:50px;">
        <col style="width:99px;">
        <tr bordercolor="#99FFFF" bgcolor="#E8E8B0" align="center">
          <td nowrap bordercolor="#99FFFF" style="width:150px;">
            <div align="center"><font color="#000000">取引先</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:100px;">
            <div align="center"><font color="#000000">産地</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:100px;">
            <div align="center"><font color="#000000">等級</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:100px;">
            <div align="center"><font color="#000000">規格</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:70px;">
            <div align="center"><font color="#000000">原単価</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:70px;">
            <div align="center"><font color="#000000">出庫単価</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:60px;">
            <div align="center"><font color="#000000">売単価</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:50px;">
            <div align="center"><font color="#000000">発注数</font></div>
          </td>
          <td nowrap bordercolor="#99FFFF" style="width:99px;">
            <div align="center"><font color="#000000">&nbsp;</font></div>
          </td>
        </tr>
      </table>
    </td>
  </tr>

  <tr>
    <td>
<%
Iterator ite = daitaiList.getBeanIterator();
String overF = "scroll";
if( !ite.hasNext() ) overF = "hidden";
%>
      <div align="left" style="overflow-y:<%=overF%>;WIDTH: 830px; HEIGHT: 253px;">
        <table valign="top" align="left" width="815px" height="" border="0" bordercolor="#CCCCCC" bgcolor="#778899" cellspacing="1" cellpadding="0" style="font-weight: bold;">
<%
popS05102_DaitaiSelBean popBean = new popS05102_DaitaiSelBean();

while(ite.hasNext()){
	popBean = (popS05102_DaitaiSelBean)ite.next();
%>
          <col style="width:150px;" align="left">
          <col style="width:100px; " align="left">
          <col style="width:100px; " align="left">
          <col style="width:100px; " align="left">
          <col style="width:70px; " align="right">
          <col style="width:70px; " align="right">
          <col style="width:60px; " align="right">
          <col style="width:50px; " align="right">
          <col style="width:99px; " align="center">

          <tr align="center" height="35" bordercolor="#99FFFF" bgcolor="#FFFFFF">

	          <%
	          String gentankaVl = "";
	          String syukotankaVl = "";
	          String baitankaVl = "";
	          String hachuQt = "";
	          String shimeFg = "";

	          //値段関連項目が存在しない場合は空白
	          if(!(popBean.getGentankaVl() == 0)){
	        	  gentankaVl = popBean.getGentankaVlString();
	          }
	          if(!(popBean.getSyukotankaVl() == 0)){
	        	  syukotankaVl = popBean.getSyukotankaVlString();
	          }
	          if(!(popBean.getBaitankaVl() == 0)){
	        	  baitankaVl = popBean.getBaitankaVlString();
	          }

	          //shimeFg==1 の場合、選択項目を押下不可にする
	          if(popBean.getShimeFg().equals("1")){
	        	  shimeFg = "disabled=true";
	          }
	          %>

	          <td style="width:150px;"><%=MDWareHTMLUtility.toLabel( popBean.getTorihikisakiCd() )%> <br> <%=MDWareHTMLUtility.toLabel( popBean.getTorihikisakiNa() )%></td>
	          <td style="width:100px;"><%=MDWareHTMLUtility.toLabel( popBean.getSantiCd() )%> <br> <%=MDWareHTMLUtility.toLabel( popBean.getSantiNa() )%> </td>
	          <td style="width:100px;"><%=MDWareHTMLUtility.toLabel( popBean.getTokaikyuCd() )%> <br> <%=MDWareHTMLUtility.toLabel( popBean.getTokaikyuNa() )%> </td>
	          <td style="width:100px;"><%=MDWareHTMLUtility.toLabel( popBean.getKikakuCd() )%> <br> <%=MDWareHTMLUtility.toLabel( popBean.getKikakuNa() )%> </td>
	          <td style="width:70px;"><%=MDWareHTMLUtility.toLabel( gentankaVl )%></td>
	          <td style="width:70px;"><%=MDWareHTMLUtility.toLabel( syukotankaVl )%></td>
	          <td style="width:60px;"><%=MDWareHTMLUtility.toLabel( baitankaVl )%></td>
	          <td style="width:50px;"><%=MDWareHTMLUtility.toLabel( popBean.getHachuQtString() )%>

	            <td style="width:99px;"><input type="button" name="select" value="選択" style="width:80px; height:30px; font-size:16px;"
	            		onClick="doCommit('<%=MDWareHTMLUtility.toLabel( popBean.getTorihikisakiCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getTorihikisakiNa() )%>',
	            		'<%=MDWareHTMLUtility.toLabel( popBean.getSantiCd() )%>' , '<%=MDWareHTMLUtility.toLabel( popBean.getSantiNa() )%>',
	            		'<%=MDWareHTMLUtility.toLabel( popBean.getTokaikyuCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getTokaikyuNa() )%>',
	            		'<%=MDWareHTMLUtility.toLabel( popBean.getKikakuCd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getKikakuNa() )%>',
	            		'<%=MDWareHTMLUtility.toLabel( popBean.getSyohinNa() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getHiKikakuNa() )%>');"　 tabIndex="<%= tabIndex++ %>" <%=shimeFg%>></td>

          </tr>
<%
}
%>
        </table>
      </div>
    </td>
  </tr>
</table>

  <span id="buttonPager" style="">
    <table width="100%">
      <col width="100%" align="center" valign="top" />
      <tr>
      <td>
			<%

			String first = "";
			String prev = "";
			String next = "";
			String last = "";

			if ( daitaiList.getCurrentPageNumber() == 1)
			{
				first = "disabled";
				prev  = "disabled";
			}
			if ( daitaiList.getCurrentPageNumber() == daitaiList.getLastPageNumber() || (daitaiList.getLastPageNumber() == 0))
			{
				next = "disabled";
				last = "disabled";
			}
			%>
			<%if( first.length() > 0 ) {%>
				<input type="button" name="headPageButton" value="先　頭" onClick="" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" disabled="true">
			<%} else {%>
				<input type="button" name="headPageButton" value="先　頭" onClick="changePage('first');" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
			<%}%>
			<%if( prev.length() > 0 ) {%>
				<input type="button" name="prevPageButton" value="前　頁" onClick="" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" disabled="true">
			<%} else {%>
				<input type="button" name="prevPageButton" value="前　頁" onClick="changePage('prev');" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
			<%}%>
			<%if( next.length() > 0 ) {%>
				<input type="button" name="nextPageButton" value="次　頁" onClick="" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" disabled="true">
			<%} else {%>
				<input type="button" name="nextPageButton" value="次　頁" onClick="changePage('next');" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
			<%}%>
			<%if( last.length() > 0 ) {%>
				<input type="button" name="lastPageButton" value="最　終" onClick="" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" disabled="true"><br />
			<%} else {%>
				<input type="button" name="lastPageButton" value="最　終" onClick="changePage('last');" class="controlButton" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>"><br />
			<%}%>
        <span id="pagingInfo" style="">( <%=daitaiList.getMaxRows()%>件中<%=daitaiList.getStartRowInPage()%>～<%=daitaiList.getEndRowInPage()%>件目 )</span></span>
      </td>
      </tr>
    </table>
  </span>

<table width="100%" border="0" cellpadding="5" cellspacing="0">
  <tr>
    <td width="100%" height="20" align="center" valign="top">
      <input  type="button" name="canceler" value="キャンセル" class="btn" onClick="javascript:self.window.top.close();" style="font-size: 16px; font-weight: bold; width:80px; height: 30px;" tabIndex="<%= tabIndex++ %>">
    </td>
  </tr>
</table>

<input type="hidden" name="ModifiedCondition" value="">
</form>
</body>