<body onload="checkSearchResult();<%=setFocus%>" bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" >
<form name="MainForm" method="post" action="app">
<input type="hidden" name="system_kb" value="<%=syotaikeiTable.getParameter("system_kb")%>" >
<input type="hidden" name="level" value="<%=syotaikeiTable.getParameter("level")%>" >
<input type="hidden" name="searchFg" value="1">
<table width="100%" border="0" cellspacing="0" cellpadding="0">
	<tr>
		<td align="center" valign="top">
			<br>
			<div class="header_sub"><%=titleStr%>検索</div>
			<%
				//*--------------
				//   大分類２
				//*--------------
				if( intLevel == 1 ) {
			%>
			<div class="term_sub">
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>大分類2名称</th>
						<td nowrap>
							<input type="text" name="term_daibunrui2_na" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("DAIBUNRUI2_KANJI_NA_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("daibunrui2_na"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>有効日</th>
						<td nowrap>
							<input type="text" name="term_yuko_dt" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("DATE_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_yuko_dt"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<td align="center" nowrap>
							<font>大分類2名称はあいまい検索となります。</font>
						</td>
					</tr>
				</table>
				<table class="term_sub_btn_area" border="0" cellspacing="0" cellpadding="3">
					<tr>    
						<td align="center">
							<input type="button" name="search" value="&emsp;検&emsp;索&emsp;" onClick="syotaikeiSearch(<%=intLevel%>);" class="controlButton" tabIndex="<%=tabIndex++%>">
							
						</td>
					</tr>
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="0" cellpadding="2">
					<tr>
						<td align="center">
							<jsp:include page="ptl000002_InfoStringList.jsp" />
						</td>
					</tr>
				</table>
			</div>
			<%
				//*--------------
				//   分類１
				//*--------------
				} else if( intLevel == 2 ) {
			%>
			<div class="term_sub">
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>大分類名称</th>
						<td nowrap>
							<input type="text" name="term_bunrui1_na" align="middle" maxlength=<%= ResorceUtil.getInstance().getPropertie("BUNRUI1_KANJI_NA_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_bunrui1_na"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>有効日</th>
						<td nowrap>
							<input type="text" name="term_yuko_dt" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("DATE_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_yuko_dt"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<td align="center" nowrap>
							<font>大分類名称はあいまい検索となります。</font>
						</td>
					</tr>
				</table>
				<table class="term_sub_btn_area" border="0" cellspacing="0" cellpadding="3">
					<tr>    
						<td align="center">
							<input type="button" name="search" value="&emsp;検&emsp;索&emsp;" onClick="syotaikeiSearch(<%=intLevel%>);" class="controlButton" tabIndex="<%=tabIndex++%>">
							
						</td>
					</tr>
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="0" cellpadding="2">
					<tr>
						<td align="center">
							<jsp:include page="ptl000002_InfoStringList.jsp" />
						</td>
					</tr>
				</table>
			</div>
			<%
				//*--------------
				//   分類２
				//*--------------
				} else if( intLevel == 3 ) {
			%>
			<div class="term_sub">
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>大分類コード</th>
						<td nowrap>
							<input type="text" name="term_bunrui1_cd" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("BUNRUI1_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_bunrui1_cd"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>中分類名称</th>
						<td nowrap>
							<input type="text" name="term_bunrui2_na" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("BUNRUI2_KANJI_NA_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_bunrui2_na"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>有効日</th>
						<td nowrap>
							<input type="text" name="term_yuko_dt" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("DATE_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_yuko_dt"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<td align="center" nowrap>
							<font>中分類名称はあいまい検索となります。</font>
						</td>
					</tr>
				</table>
				<table class="term_sub_btn_area" border="0" cellspacing="0" cellpadding="3">
					<tr>    
						<td align="center">
							<input type="button" name="search" value="&emsp;検&emsp;索&emsp;" onClick="syotaikeiSearch(<%=intLevel%>);" class="controlButton" tabIndex="<%=tabIndex++%>">
							
						</td>
					</tr>
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="0" cellpadding="2">
					<tr>
						<td align="center">
							<jsp:include page="ptl000002_InfoStringList.jsp" />
						</td>
					</tr>
				</table>
			</div>
			<%
				//*--------------
				//   分類３
				//*--------------
				} else if( intLevel == 4 ) {
			%>
			<div class="term_sub">
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>中分類コード</th>
						<td nowrap>
							<input type="text" name="term_bunrui2_cd" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("BUNRUI2_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_bunrui2_cd"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>中小分類名称</th>
						<td nowrap>
							<input type="text" name="term_bunrui3_na" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("BUNRUI3_KANJI_NA_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_bunrui3_na"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>有効日</th>
						<td nowrap>
							<input type="text" name="term_yuko_dt" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("DATE_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_yuko_dt"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<td align="center" nowrap>
							<font>中小分類名称はあいまい検索となります。</font>
						</td>
					</tr>
				</table>
				<table class="term_sub_btn_area" border="0" cellspacing="0" cellpadding="3">
					<tr>    
						<td align="center">
							<input type="button" name="search" value="&emsp;検&emsp;索&emsp;" onClick="syotaikeiSearch(<%=intLevel%>);" class="controlButton" tabIndex="<%=tabIndex++%>">
							
						</td>
					</tr>
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="0" cellpadding="2">
					<tr>
						<td align="center">
							<jsp:include page="ptl000002_InfoStringList.jsp" />
						</td>
					</tr>
				</table>
			</div>
			<%
				//*--------------
				//   分類５
				//*--------------
				} else if( intLevel == 5 ) {
			%>
			<div class="term_sub">
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>中小分類コード</th>
						<td nowrap>
							<input type="text" name="term_bunrui3_cd" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("BUNRUI3_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_bunrui3_cd"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>小分類名称</th>
						<td nowrap>
							<input type="text" name="term_bunrui5_na" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("BUNRUI5_KANJI_NA_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_bunrui5_na"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<th nowrap>有効日</th>
						<td nowrap>
							<input type="text" name="term_yuko_dt" align="middle" maxlength="<%= ResorceUtil.getInstance().getPropertie("DATE_LENGTH") %>" value="<%=MDWareHTMLUtility.toText(syotaikeiTable.getParameter("term_yuko_dt"))%>" tabIndex="<%= tabIndex++ %>">
						</td>
					</tr>
				</table>
				<table class="term_sub" border="0" cellspacing="2" cellpadding="3">
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="2" cellpadding="3">
					<tr>
						<td align="center" nowrap>
							<font>小分類名称はあいまい検索となります。</font>
						</td>
					</tr>
				</table>
				<table class="term_sub_btn_area" border="0" cellspacing="0" cellpadding="3">
					<tr>    
						<td align="center">
							<input type="button" name="search" value="&emsp;検&emsp;索&emsp;" onClick="syotaikeiSearch(<%=intLevel%>);" class="controlButton" tabIndex="<%=tabIndex++%>">
							
						</td>
					</tr>
				</table>
				<table class="term_sub_msg_area" border="0" cellspacing="0" cellpadding="2">
					<tr>
						<td align="center">
							<jsp:include page="ptl000002_InfoStringList.jsp" />
						</td>
					</tr>
				</table>
			</div>
			<%
				}
			%>
			<!-- 遷移画面先指定 -->
			<input type="hidden" name="JobID"         value="">
			<input type="hidden" name="movePage"       value="">
			<!-- 次ページ指定 -->
			<input type=hidden name=move value="">
			<%
				int index = 0;
				//*--------------
				//		    大分類２
				//*--------------
				if( intLevel == 1 ) {
			%>
			<div class="list1_sub">
				<table align="center" style="border: 0px;">
					<tr>
						<td align="left">
							<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<th class="list1_sub_bunrui_cd">大分類2コード</th>
									<th class="list1_sub_bunrui_na" noWrap>大分類2名称</th>
									<th class="list1_sub_sentaku"></th>
								</tr>
							</table>
							<%
								Iterator ite = syotaikeiList.getBeanIterator();
								String overF = "scroll";
								if( !ite.hasNext() ) overF = "hidden";
							%>
							<div style="overflow-y: <%=overF%>; HEIGHT: 256px;">
								<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<%
									MST99005_BmKindLineUnitBean popBean = new MST99005_BmKindLineUnitBean();
									
									while(ite.hasNext()){
										popBean = (MST99005_BmKindLineUnitBean)ite.next();
									
								%>

									<tr>
										<td class="list1_sub_bunrui_cd" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Cd() )%>
										</td>
										<td class="list1_sub_bunrui_na" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Na() )%>
										</td>
										<td class="list1_sub_sentaku" align="center">
											<input type="button" name="select" value="選択" onClick="doCommit( '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Na() )%>', '', '', '', '', '', '' );" tabIndex="<%= tabIndex++ %>" class="controlButton">
											<input type="hidden" name="cd1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Cd() )%>">
											<input type="hidden" name="na1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Na() )%>">
										</td>
									</tr>
								<%
										index++;
									}
								%>
								</table>
							</div>
						</td>
					</tr>
				</table>
			</div>
			<%
				//*--------------
				//    分類１
				//*--------------
				} else if( intLevel == 2 ) {
			%>
			<div class="list1_sub">
				<table align="center" style="border: 0px;">
					<tr>
						<td align="left">
							<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<th class="list1_sub_bunrui_cd">大分類コード</th>
									<th class="list1_sub_bunrui_na" noWrap>大分類名称</th>
									<th class="list1_sub_sentaku"></th>
								</tr>
							</table>
							<%
								Iterator ite = syotaikeiList.getBeanIterator();
								String overF = "scroll";
								if( !ite.hasNext() ) overF = "hidden";
							%>
							<div style="overflow-y: <%=overF%>; HEIGHT: 256px;">
								<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<%
									MST99005_BmKindLineUnitBean popBean = new MST99005_BmKindLineUnitBean();
									
									while(ite.hasNext()){
										popBean = (MST99005_BmKindLineUnitBean)ite.next();
									
								%>

									<tr>
										<td class="list1_sub_bunrui_cd" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Cd() )%>
										</td>
										<td class="list1_sub_bunrui_na" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Na() )%>
										</td>
										<td class="list1_sub_sentaku" align="center">
											<input type="button" name="select" value="選択" onClick="doCommit( '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Na() )%>', '', '', '', '' );" tabIndex="<%= tabIndex++ %>" class="controlButton">
											<input type="hidden" name="cd1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Cd() )%>">
											<input type="hidden" name="na1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Na() )%>">
										</td>
									</tr>
								<%
										index++;
									}
								%>
								</table>
							</div>
						</td>
					</tr>
				</table>
			</div>
			<%
				//*--------------
				//   分類２
				//*--------------
				} else if( intLevel == 3 ) {
			%>
			<div class="list1_sub">
				<table align="center" style="border: 0px;">
					<tr>
						<td align="left">
							<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<th class="list1_sub_bunrui_cd">中分類コード</th>
									<th class="list1_sub_bunrui_na" noWrap>中分類名称</th>
									<th class="list1_sub_sentaku"></th>
								</tr>
							</table>
							<%
								Iterator ite = syotaikeiList.getBeanIterator();
								String overF = "scroll";
								if( !ite.hasNext() ) overF = "hidden";
							%>
							<div style="overflow-y: <%=overF%>; HEIGHT: 256px;">
								<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<%
									MST99005_BmKindLineUnitBean popBean = new MST99005_BmKindLineUnitBean();
									
									while(ite.hasNext()){
										popBean = (MST99005_BmKindLineUnitBean)ite.next();
									
								%>

									<tr>
										<td class="list1_sub_bunrui_cd" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Cd() )%>
										</td>
										<td class="list1_sub_bunrui_na" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Na() )%>
										</td>
										<td class="list1_sub_sentaku" align="center">
											<input type="button" name="select" value="選択" onClick="doCommit( '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Na() )%>', '', '' );" tabIndex="<%= tabIndex++ %>" class="controlButton">
											<input type="hidden" name="cd1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Cd() )%>">
											<input type="hidden" name="na1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Na() )%>">
										</td>
									</tr>
								<%
										index++;
									}
								%>
								</table>
							</div>
						</td>
					</tr>
				</table>
			</div>
			<%
				//*--------------
				//   分類３
				//*--------------
				} else if( intLevel == 4 ) {
			%>
			<div class="list1_sub">
				<table align="center" style="border: 0px;">
					<tr>
						<td align="left">
							<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<th class="list1_sub_bunrui_cd">中小分類コード</th>
									<th class="list1_sub_bunrui_na" noWrap>中小分類名称</th>
									<th class="list1_sub_sentaku"></th>
								</tr>
							</table>
							<%
								Iterator ite = syotaikeiList.getBeanIterator();
								String overF = "scroll";
								if( !ite.hasNext() ) overF = "hidden";
							%>
							<div style="overflow-y: <%=overF%>; HEIGHT: 256px;">
								<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<%
									MST99005_BmKindLineUnitBean popBean = new MST99005_BmKindLineUnitBean();
									
									while(ite.hasNext()){
										popBean = (MST99005_BmKindLineUnitBean)ite.next();
									
								%>

									<tr>
										<td class="list1_sub_bunrui_cd" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getBunrui3Cd() )%>
										</td>
										<td class="list1_sub_bunrui_na" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getBunrui3Na() )%>
										</td>
										<td class="list1_sub_sentaku" align="center">
											<input type="button" name="select" value="選択" onClick="doCommit( '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Na() )%>','<%=MDWareHTMLUtility.toLabel( popBean.getBunrui3Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui3Na() )%>', '', '' ) ;" tabIndex="<%= tabIndex++ %>" class="controlButton">
											<input type="hidden" name="cd1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getBunrui3Cd() )%>">
											<input type="hidden" name="na1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getBunrui3Na() )%>">
										</td>
									</tr>
								<%
										index++;
									}
								%>
								</table>
							</div>
						</td>
					</tr>
				</table>
			</div>
			<%
				//*--------------
				//   分類５
				//*--------------
				} else if( intLevel == 5 ) {
			%>
			<div class="list1_sub">
				<table align="center" style="border: 0px;">
					<tr>
						<td align="left">
							<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<th class="list1_sub_bunrui_cd">小分類コード</th>
									<th class="list1_sub_bunrui_na" noWrap>小分類名称</th>
									<th class="list1_sub_sentaku"></th>
								</tr>
							</table>
							<%
								Iterator ite = syotaikeiList.getBeanIterator();
								String overF = "scroll";
								if( !ite.hasNext() ) overF = "hidden";
							%>
							<div style="overflow-y: <%=overF%>; HEIGHT: 256px;">
								<table class="list1_sub" border="0" cellpadding="0" cellspacing="0">
								<%
									MST99005_BmKindLineUnitBean popBean = new MST99005_BmKindLineUnitBean();
									
									while(ite.hasNext()){
										popBean = (MST99005_BmKindLineUnitBean)ite.next();
									
								%>

									<tr>
										<td class="list1_sub_bunrui_cd" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getBunrui5Cd() )%>
										</td>
										<td class="list1_sub_bunrui_na" style="text-align:left;">
											&nbsp;<%=MDWareHTMLUtility.toLabel( popBean.getBunrui5Na() )%>
										</td>
										<td class="list1_sub_sentaku" align="center">
											<input type="button" name="select" value="選択" onClick="doCommit( '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui1Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getDaiBunrui2Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui1Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui2Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui3Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui3Na() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui5Cd() )%>', '<%=MDWareHTMLUtility.toLabel( popBean.getBunrui5Na() )%>' );" tabIndex="<%= tabIndex++ %>" class="controlButton">
											<input type="hidden" name="cd1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getBunrui5Cd() )%>">
											<input type="hidden" name="na1_<%=index%>" value="<%=MDWareHTMLUtility.toLabel( popBean.getBunrui5Na() )%>">
										</td>
									</tr>
								<%
										index++;
									}
								%>
								</table>
							</div>
						</td>
					</tr>
				</table>
			</div>
			<%
				}
			%>
			<br>
			<table class="list1_sub_btn_area" >
			  <tr>
			    <td align="center">
			      <input  type="button" name="canceler" value="キャンセル" class="btn" onClick="javascript:window.close()" tabIndex="<%= tabIndex++ %>">
			    </td>
			  </tr>
			</table>
			<input type="hidden" name="ModifiedCondition" value="">
		</td>
	</tr>
</table>
</form>
<script language="JavaScript">


<%

	// 1件の場合
	if (syotaikeiList.getMaxRows() == 1) {


		if ("1".equals(request.getParameter("searchFg"))) {

			// 検索ボタンがクリックされた場合ここを通る
			// 先頭のデータを親画面に設定する


%>
<%
		} else {

			// 初期検索又は自動再検索の場合ここを通る

			if (request.getParameter("searchFg") == null) {

				// 初期検索の場合は呼出し元画面と変更がなければ先頭のデータを親画面に設定
				// 変更があれば再検索を行う

%>

	// 先頭のデータを設定
	setKb = "1";

<%
			} else if ("3".equals(request.getParameter("searchFg"))){

				// 自動再検索かつ初期検索で0件だった場合は全件検索となる=画面はそのまま(結果1件でもそのまま)

%>



<%
			}
		}
	} else if (request.getParameter("searchFg") == null && syotaikeiList.getMaxRows() == 0 && !hasError) {

		// 初期検索で0件の場合は再検索を行う

%>

	document.getElementById("searchFg").value = "3";


	// 再検索
	setKb = "2";
<%


	}
%>

</script>
</body>